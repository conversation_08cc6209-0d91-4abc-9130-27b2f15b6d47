/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette */
    --primary-gold: #D4AF37;
    --secondary-gold: #F4E4BC;
    --dark-charcoal: #2C2C2C;
    --light-gray: #F8F8F8;
    --white: #FFFFFF;
    --black: #000000;
    --earth-brown: #8B4513;
    --warm-beige: #F5F5DC;
    
    /* Typography */
    --font-heading: 'Playfair Display', serif;
    --font-body: 'Lato', sans-serif;
    
    /* Spacing */
    --section-padding: 100px 0;
    --container-padding: 0 20px;
    
    /* Transitions */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-slow: all 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: var(--font-body);
    line-height: 1.6;
    color: var(--dark-charcoal);
    background-color: var(--white);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: var(--container-padding);
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
    font-family: var(--font-heading);
    font-weight: 600;
    line-height: 1.2;
    margin-bottom: 1rem;
}

h1 { font-size: clamp(2.5rem, 5vw, 4rem); }
h2 { font-size: clamp(2rem, 4vw, 3rem); }
h3 { font-size: clamp(1.5rem, 3vw, 2rem); }
h4 { font-size: clamp(1.25rem, 2.5vw, 1.5rem); }

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
    color: var(--dark-charcoal);
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 15px 30px;
    text-decoration: none;
    border-radius: 50px;
    font-weight: 500;
    font-size: 1rem;
    text-align: center;
    transition: var(--transition-smooth);
    cursor: pointer;
    border: 2px solid transparent;
    position: relative;
    overflow: hidden;
}

.btn-primary {
    background: linear-gradient(135deg, var(--primary-gold), var(--earth-brown));
    color: var(--white);
    box-shadow: 0 8px 25px rgba(212, 175, 55, 0.3);
}

.btn-primary:hover {
    transform: translateY(-3px);
    box-shadow: 0 12px 35px rgba(212, 175, 55, 0.4);
}

.btn-secondary {
    background: transparent;
    color: var(--primary-gold);
    border: 2px solid var(--primary-gold);
}

.btn-secondary:hover {
    background: var(--primary-gold);
    color: var(--white);
    transform: translateY(-3px);
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    width: 100%;
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(10px);
    z-index: 1000;
    transition: var(--transition-smooth);
    padding: 1rem 0;
}

.navbar.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
    padding: 0.5rem 0;
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-logo h2 {
    font-family: var(--font-heading);
    color: var(--primary-gold);
    margin: 0;
    font-size: 1.8rem;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 2rem;
}

.nav-link {
    text-decoration: none;
    color: var(--dark-charcoal);
    font-weight: 500;
    transition: var(--transition-smooth);
    position: relative;
}

.nav-link:hover {
    color: var(--primary-gold);
}

.nav-link::after {
    content: '';
    position: absolute;
    bottom: -5px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-gold);
    transition: var(--transition-smooth);
}

.nav-link:hover::after {
    width: 100%;
}

.cta-button {
    background: var(--primary-gold);
    color: var(--white) !important;
    padding: 10px 20px;
    border-radius: 25px;
    margin-left: 1rem;
}

.cta-button:hover {
    background: var(--earth-brown);
    transform: translateY(-2px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
}

.bar {
    width: 25px;
    height: 3px;
    background: var(--dark-charcoal);
    margin: 3px 0;
    transition: var(--transition-smooth);
}

/* Hero Section */
.hero {
    height: 100vh;
    background: linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)),
                url('https://images.unsplash.com/photo-1555244162-803834f70033?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2070&q=80');
    background-size: cover;
    background-position: center;
    background-attachment: fixed;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    color: var(--white);
    position: relative;
}

.hero-content {
    max-width: 800px;
    padding: 0 20px;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    font-size: clamp(3rem, 6vw, 5rem);
    margin-bottom: 1.5rem;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.hero-subtitle {
    font-size: clamp(1.2rem, 2.5vw, 1.5rem);
    margin-bottom: 2.5rem;
    opacity: 0.9;
    font-weight: 300;
}

.hero-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
}

.hero-scroll {
    position: absolute;
    bottom: 30px;
    left: 50%;
    transform: translateX(-50%);
}

.scroll-indicator {
    color: var(--white);
    font-size: 1.5rem;
    animation: bounce 2s infinite;
    text-decoration: none;
}

/* Section Headers */
.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    color: var(--dark-charcoal);
    margin-bottom: 1rem;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, var(--primary-gold), var(--earth-brown));
}

.section-subtitle {
    font-size: 1.2rem;
    color: #666;
    font-style: italic;
}

/* About Section */
.about {
    padding: var(--section-padding);
    background: var(--light-gray);
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: center;
}

.about-text h3 {
    color: var(--primary-gold);
    margin-bottom: 1.5rem;
}

.about-text p {
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
    line-height: 1.8;
}

.about-stats {
    display: flex;
    gap: 2rem;
    margin-top: 2rem;
}

.stat {
    text-align: center;
}

.stat h4 {
    font-size: 2.5rem;
    color: var(--primary-gold);
    margin-bottom: 0.5rem;
}

.stat p {
    font-size: 0.9rem;
    color: #666;
    margin: 0;
}

.about-image {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.about-image img {
    width: 100%;
    height: 500px;
    object-fit: cover;
    transition: var(--transition-slow);
}

.about-image:hover img {
    transform: scale(1.05);
}

/* Services Section */
.services {
    padding: var(--section-padding);
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 2rem;
}

.service-card {
    background: var(--white);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: var(--transition-smooth);
    position: relative;
    overflow: hidden;
}

.service-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-gold), var(--earth-brown));
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 50px rgba(0, 0, 0, 0.15);
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-gold), var(--earth-brown));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: var(--white);
}

.service-card h3 {
    color: var(--dark-charcoal);
    margin-bottom: 1rem;
}

.service-card p {
    margin-bottom: 1.5rem;
    color: #666;
}

.service-card ul {
    list-style: none;
    text-align: left;
}

.service-card li {
    padding: 0.5rem 0;
    position: relative;
    padding-left: 1.5rem;
    color: #666;
}

.service-card li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--primary-gold);
    font-weight: bold;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0) translateX(-50%);
    }
    40% {
        transform: translateY(-10px) translateX(-50%);
    }
    60% {
        transform: translateY(-5px) translateX(-50%);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .hamburger {
        display: flex;
    }
    
    .nav-menu {
        position: fixed;
        left: -100%;
        top: 70px;
        flex-direction: column;
        background-color: var(--white);
        width: 100%;
        text-align: center;
        transition: var(--transition-smooth);
        box-shadow: 0 10px 27px rgba(0, 0, 0, 0.05);
        padding: 2rem 0;
    }
    
    .nav-menu.active {
        left: 0;
    }
    
    .nav-menu li {
        margin: 1rem 0;
    }
    
    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }
    
    .about-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .about-stats {
        justify-content: center;
    }
    
    .services-grid {
        grid-template-columns: 1fr;
    }
    
    .service-card {
        padding: 2rem;
    }
}

/* Menu Section */
.menu {
    padding: var(--section-padding);
    background: var(--light-gray);
}

.menu-filters {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 3rem;
    flex-wrap: wrap;
}

.filter-btn {
    background: transparent;
    border: 2px solid var(--primary-gold);
    color: var(--primary-gold);
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: var(--transition-smooth);
    font-weight: 500;
}

.filter-btn:hover,
.filter-btn.active {
    background: var(--primary-gold);
    color: var(--white);
}

.menu-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.menu-item {
    background: var(--white);
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    transition: var(--transition-smooth);
}

.menu-item:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
}

.menu-item-image {
    height: 200px;
    overflow: hidden;
}

.menu-item-image img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.menu-item:hover .menu-item-image img {
    transform: scale(1.1);
}

.menu-item-content {
    padding: 1.5rem;
}

.menu-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 0.5rem;
}

.menu-item h4 {
    color: var(--dark-charcoal);
    margin: 0;
}

.menu-item-price {
    color: var(--primary-gold);
    font-weight: bold;
    font-size: 1.1rem;
}

.menu-item p {
    color: #666;
    font-size: 0.95rem;
    margin: 0;
}

/* Gallery Section */
.gallery {
    padding: var(--section-padding);
}

.gallery-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1rem;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 15px;
    aspect-ratio: 1;
    cursor: pointer;
}

.gallery-item img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: var(--transition-slow);
}

.gallery-item:hover img {
    transform: scale(1.1);
}

.gallery-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(212, 175, 55, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: var(--transition-smooth);
}

.gallery-item:hover .gallery-overlay {
    opacity: 1;
}

.gallery-overlay i {
    color: var(--white);
    font-size: 2rem;
}

/* Testimonials Section */
.testimonials {
    padding: var(--section-padding);
    background: var(--light-gray);
    position: relative;
}

.testimonials-carousel {
    max-width: 800px;
    margin: 0 auto;
    position: relative;
    overflow: hidden;
}

.testimonial {
    text-align: center;
    padding: 2rem;
    display: none;
}

.testimonial.active {
    display: block;
    animation: fadeInUp 0.6s ease-out;
}

.testimonial-content {
    font-size: 1.2rem;
    font-style: italic;
    color: var(--dark-charcoal);
    margin-bottom: 2rem;
    line-height: 1.8;
}

.testimonial-author {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.testimonial-avatar {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    overflow: hidden;
}

.testimonial-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.testimonial-info h5 {
    color: var(--primary-gold);
    margin: 0;
    font-size: 1.1rem;
}

.testimonial-info p {
    color: #666;
    margin: 0;
    font-size: 0.9rem;
}

.carousel-controls {
    display: flex;
    justify-content: center;
    gap: 1rem;
    margin-top: 2rem;
}

.carousel-btn {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: var(--primary-gold);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: var(--transition-smooth);
    display: flex;
    align-items: center;
    justify-content: center;
}

.carousel-btn:hover {
    background: var(--earth-brown);
    transform: scale(1.1);
}

@media (max-width: 480px) {
    :root {
        --section-padding: 60px 0;
        --container-padding: 0 15px;
    }

    .hero-title {
        font-size: 2.5rem;
    }

    .hero-subtitle {
        font-size: 1.1rem;
    }

    .about-stats {
        flex-direction: column;
        gap: 1rem;
    }

    .menu-filters {
        gap: 0.5rem;
    }

    .filter-btn {
        padding: 8px 15px;
        font-size: 0.9rem;
    }

    .gallery-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .testimonial {
        padding: 1rem;
    }

    .testimonial-content {
        font-size: 1.1rem;
    }
}

/* Contact Section */
.contact {
    padding: var(--section-padding);
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
    align-items: start;
}

.contact-info {
    display: flex;
    flex-direction: column;
    gap: 2rem;
}

.contact-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
}

.contact-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-gold), var(--earth-brown));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: var(--white);
    font-size: 1.2rem;
    flex-shrink: 0;
}

.contact-details h4 {
    color: var(--dark-charcoal);
    margin-bottom: 0.5rem;
}

.contact-details p {
    color: #666;
    margin: 0;
}

.social-links {
    display: flex;
    gap: 1rem;
    margin-top: 1rem;
}

.social-link {
    width: 45px;
    height: 45px;
    background: var(--primary-gold);
    color: var(--white);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: var(--transition-smooth);
}

.social-link:hover {
    background: var(--earth-brown);
    transform: translateY(-3px);
}

/* Contact Form */
.contact-form {
    background: var(--light-gray);
    padding: 2.5rem;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.form-group {
    position: relative;
    margin-bottom: 2rem;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 15px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 1rem;
    font-family: var(--font-body);
    transition: var(--transition-smooth);
    background: var(--white);
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: var(--primary-gold);
    box-shadow: 0 0 0 3px rgba(212, 175, 55, 0.1);
}

.form-group label {
    position: absolute;
    top: 15px;
    left: 15px;
    color: #999;
    font-size: 1rem;
    transition: var(--transition-smooth);
    pointer-events: none;
    background: var(--white);
    padding: 0 5px;
}

.form-group input:focus + label,
.form-group input:not(:placeholder-shown) + label,
.form-group select:focus + label,
.form-group select:not([value=""]) + label,
.form-group textarea:focus + label,
.form-group textarea:not(:placeholder-shown) + label {
    top: -8px;
    left: 10px;
    font-size: 0.85rem;
    color: var(--primary-gold);
}

.form-group textarea {
    resize: vertical;
    min-height: 120px;
}

.error-message {
    color: #e74c3c;
    font-size: 0.85rem;
    margin-top: 0.25rem;
    display: none;
}

.error-message.show {
    display: block;
}

.form-help {
    color: #666;
    font-size: 0.85rem;
    margin-top: 0.25rem;
}

/* Focus styles for accessibility */
button:focus,
input:focus,
select:focus,
textarea:focus,
a:focus {
    outline: 2px solid var(--primary-gold);
    outline-offset: 2px;
}

/* Skip link for screen readers */
.skip-link {
    position: absolute;
    top: -40px;
    left: 6px;
    background: var(--primary-gold);
    color: var(--white);
    padding: 8px;
    text-decoration: none;
    border-radius: 4px;
    z-index: 10000;
}

.skip-link:focus {
    top: 6px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --primary-gold: #B8860B;
        --dark-charcoal: #000000;
        --light-gray: #F0F0F0;
    }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    html {
        scroll-behavior: auto;
    }
}

/* Footer */
.footer {
    background: var(--dark-charcoal);
    color: var(--white);
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h3,
.footer-section h4 {
    color: var(--primary-gold);
    margin-bottom: 1rem;
}

.footer-section p {
    color: #ccc;
    margin-bottom: 0.5rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #ccc;
    text-decoration: none;
    transition: var(--transition-smooth);
}

.footer-section ul li a:hover {
    color: var(--primary-gold);
}

.footer-section i {
    margin-right: 0.5rem;
    color: var(--primary-gold);
}

.footer-bottom {
    border-top: 1px solid #444;
    padding-top: 1rem;
    text-align: center;
}

.footer-bottom p {
    color: #999;
    margin: 0;
}

/* Additional Responsive Styles */
@media (max-width: 768px) {
    .contact-content {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .contact-form {
        padding: 2rem;
    }

    .footer-content {
        grid-template-columns: 1fr;
        text-align: center;
    }
}

/* Utility Classes */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: var(--transition-slow);
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

.text-center {
    text-align: center;
}

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 1rem; }
.mb-2 { margin-bottom: 2rem; }
.mb-3 { margin-bottom: 3rem; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 1rem; }
.mt-2 { margin-top: 2rem; }
.mt-3 { margin-top: 3rem; }
