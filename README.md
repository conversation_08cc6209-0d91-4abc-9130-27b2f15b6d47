# Elegant Catering Website

A modern, elegant, and user-friendly website for a premium catering services business. Built with HTML5, CSS3, and vanilla JavaScript, featuring a luxurious design with smooth animations and full responsiveness.

## Features

### Design & Visual Style
- **Luxurious and modern aesthetic** with clean layout
- **Elegant color palette**: White, gold (#D4AF37), charcoal (#2C2C2C), and earth tones
- **Premium typography**: Playfair Display for headings, Lato for body text
- **High-quality imagery** with smooth hover effects
- **Smooth animations and transitions** throughout the site

### Pages & Sections
1. **Homepage** - Engaging hero section with call-to-action
2. **About Us** - Company story, values, and statistics
3. **Services** - Wedding, corporate, and private party catering
4. **Menu** - Interactive filterable menu with categories
5. **Gallery** - Photo showcase of events and dishes
6. **Testimonials** - Client reviews in carousel format
7. **Contact/Booking** - Contact form with validation and business info

### Interactive Features
- **Sticky navigation** with smooth scroll effects
- **Mobile hamburger menu** with smooth animations
- **Menu filtering system** with category buttons
- **Testimonials carousel** with navigation controls
- **Contact form** with real-time validation
- **Scroll animations** for enhanced user experience
- **Image hover effects** and gallery interactions

### Technical Features
- **Fully responsive design** (mobile-first approach)
- **SEO optimized** with proper meta tags and structured data
- **Accessibility compliant** (WCAG 2.1 guidelines)
- **Performance optimized** with lazy loading and efficient animations
- **Cross-browser compatible**
- **Clean, semantic HTML5**
- **Modern CSS3** with custom properties and flexbox/grid
- **Vanilla JavaScript** (no dependencies)

### Accessibility Features
- **ARIA labels** and roles for screen readers
- **Keyboard navigation** support
- **Focus indicators** for all interactive elements
- **Skip links** for screen reader users
- **High contrast mode** support
- **Reduced motion** support for users with vestibular disorders
- **Form validation** with clear error messages
- **Alt text** for all images

### SEO Features
- **Meta tags** for search engines and social media
- **Open Graph** and Twitter Card support
- **Semantic HTML** structure
- **Proper heading hierarchy**
- **Canonical URLs**
- **Structured data** ready

## File Structure

```
├── index.html          # Main HTML file
├── styles.css          # All CSS styles
├── script.js           # JavaScript functionality
└── README.md           # This file
```

## Getting Started

1. **Clone or download** the project files
2. **Open index.html** in a web browser
3. **No build process required** - it's ready to use!

## Customization

### Colors
Update the CSS custom properties in `:root` to change the color scheme:
```css
:root {
    --primary-gold: #D4AF37;
    --secondary-gold: #F4E4BC;
    --dark-charcoal: #2C2C2C;
    --light-gray: #F8F8F8;
    /* ... */
}
```

### Content
- **Menu items**: Edit the `menuItems` array in `script.js`
- **Testimonials**: Edit the `testimonials` array in `script.js`
- **Gallery images**: Edit the `galleryImages` array in `script.js`
- **Contact information**: Update the contact section in `index.html`

### Images
Replace the Unsplash URLs with your own images:
- Hero background image
- About section image
- Menu item images
- Gallery images
- Testimonial avatars

## Browser Support

- **Chrome** 60+
- **Firefox** 60+
- **Safari** 12+
- **Edge** 79+
- **Mobile browsers** (iOS Safari, Chrome Mobile)

## Performance

- **Optimized images** with proper sizing and lazy loading
- **Efficient animations** using CSS transforms and opacity
- **Minimal JavaScript** with debounced scroll events
- **CSS Grid and Flexbox** for efficient layouts
- **Web fonts** loaded efficiently with font-display: swap

## Deployment

### Static Hosting
Upload all files to any static hosting service:
- **Netlify** (recommended)
- **Vercel**
- **GitHub Pages**
- **AWS S3**
- **Traditional web hosting**

### Domain Setup
1. Purchase a domain (e.g., elegantcatering.com)
2. Point DNS to your hosting provider
3. Update canonical URLs in the HTML
4. Set up SSL certificate

## Form Integration

The contact form currently shows a success message. To make it functional:

1. **Backend Integration**: Connect to a server-side script (PHP, Node.js, etc.)
2. **Email Service**: Use services like EmailJS, Formspree, or Netlify Forms
3. **CRM Integration**: Connect to customer management systems

## Maintenance

- **Update images** regularly with fresh content
- **Review testimonials** and add new ones
- **Update menu items** seasonally
- **Monitor performance** with tools like Google PageSpeed Insights
- **Check accessibility** with tools like WAVE or axe

## License

This project is open source and available under the [MIT License](LICENSE).

## Support

For questions or customization requests, please contact the development team.

---

**Built with ❤️ for premium catering businesses**
